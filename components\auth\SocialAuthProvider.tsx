"use client";
import { ReactNode, createContext, useContext } from "react";
import { GoogleOAuthProvider } from "@react-oauth/google";

// LinkedIn config type and context
export type LinkedInConfig = {
  clientId: string;
  redirectUri: string;
};

const LinkedInContext = createContext<LinkedInConfig | undefined>(undefined);

export const useLinkedInConfig = () => {
  const context = useContext(LinkedInContext);
  if (!context) throw new Error("useLinkedInConfig must be used within SocialAuthProvider");
  return context;
};

export function SocialAuthProvider({ children }: { children: ReactNode }) {
  const linkedInConfig: LinkedInConfig = {
    clientId: process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID || "LINKEDIN_CLIENT_ID",
    redirectUri: process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI || "http://localhost:3000/auth/linkedin/callback",
  };

  return (
    <GoogleOAuthProvider clientId={process.env.GOOGLE_CLIENT_ID!}>
      <LinkedInContext.Provider value={linkedInConfig}>
        {children}
      </LinkedInContext.Provider>
    </GoogleOAuthProvider>
  );
} 