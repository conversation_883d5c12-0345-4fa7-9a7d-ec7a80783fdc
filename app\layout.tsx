import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { StoreProvider } from '@/store/providers';
import { Suspense } from 'react';
import { PageLoader } from '@/components/ui/loading-spinner';
import { Toaster } from 'react-hot-toast';
import HeaderVisibility from '@/components/layout/HeaderVisibility';
import GlobalLoaderManager from '@/components/GlobalLoaderManager';
import { SocialAuthProvider } from '@/components/auth/SocialAuthProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'talentloop',
  description:
    'AI-powered talent matching platform that connects exceptional candidates with their dream jobs and helps employers find perfectly vetted talent.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link
          rel="icon"
          href="/assets/logo/TalentLoop.svg"
          type="image/svg+xml"
        />
      </head>
      <body className={inter.className}>
        <StoreProvider>
          <SocialAuthProvider>
            <HeaderVisibility />
            <GlobalLoaderManager />
            <Suspense fallback={<PageLoader />}>{children}</Suspense>
          </SocialAuthProvider>
        </StoreProvider>
        <Toaster
          position="top-right"
          toastOptions={{
            style: {
              background: '#2563eb', // Main blue
              color: '#fff',
              borderRadius: '8px',
              fontWeight: 500,
            },
            success: {
              style: {
                background: '#2563eb',
                color: '#fff',
              },
            },
            error: {
              style: {
                background: '#ef4444', // Soft red for errors
                color: '#fff',
              },
            },
          }}
        />
      </body>
    </html>
  );
}
